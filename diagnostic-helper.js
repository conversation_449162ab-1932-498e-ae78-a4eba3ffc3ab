/*
 * Diagnostic Helper for Enhanced Regex Find/Replace Plugin
 * This script helps diagnose text extraction and Unicode issues
 */

var regexDiagnostics = {
    
    // Test text extraction capabilities
    testTextExtraction: function(target) {
        console.println("=== PDF Text Extraction Diagnostics ===");
        
        if (!target || typeof target.numPages === 'undefined') {
            console.println("❌ Error: No document target available");
            return false;
        }
        
        console.println("📄 Document has " + target.numPages + " pages");
        
        // Test different text extraction methods
        for (let pageNum = 0; pageNum < Math.min(target.numPages, 3); pageNum++) {
            console.println("\n--- Page " + (pageNum + 1) + " ---");
            
            // Method 1: Word-by-word extraction
            try {
                if (typeof target.getPageNumWords !== 'undefined') {
                    let numWords = target.getPageNumWords(pageNum);
                    console.println("📊 Word count: " + numWords);
                    
                    if (numWords > 0) {
                        // Sample first few words
                        let sampleText = "";
                        for (let i = 0; i < Math.min(numWords, 20); i++) {
                            try {
                                let word = target.getPageNthWord(pageNum, i);
                                sampleText += word + " ";
                            } catch (e) {
                                console.println("⚠️  Error getting word " + i + ": " + e.message);
                            }
                        }
                        console.println("📝 Sample text: " + sampleText.substring(0, 200));
                        
                        // Check for Chinese characters
                        let chineseChars = sampleText.match(/[\u4e00-\u9fff]/g);
                        if (chineseChars) {
                            console.println("🈳 Chinese characters found: " + chineseChars.length);
                            console.println("🈳 Sample Chinese: " + chineseChars.slice(0, 10).join(""));
                        } else {
                            console.println("❌ No Chinese characters detected in sample");
                        }
                    }
                } else {
                    console.println("❌ getPageNumWords method not available");
                }
            } catch (e) {
                console.println("❌ Word extraction error: " + e.message);
            }
            
            // Method 2: Direct page text (if available)
            try {
                if (typeof target.getPageText !== 'undefined') {
                    let pageText = target.getPageText(pageNum);
                    if (pageText) {
                        console.println("📄 Direct page text length: " + pageText.length);
                        console.println("📝 Direct text sample: " + pageText.substring(0, 200));
                        
                        // Check for Chinese characters
                        let chineseChars = pageText.match(/[\u4e00-\u9fff]/g);
                        if (chineseChars) {
                            console.println("🈳 Chinese characters in direct text: " + chineseChars.length);
                        }
                    } else {
                        console.println("❌ Direct page text returned empty");
                    }
                } else {
                    console.println("❌ getPageText method not available");
                }
            } catch (e) {
                console.println("❌ Direct text extraction error: " + e.message);
            }
        }
        
        return true;
    },
    
    // Test annotation text extraction
    testAnnotationExtraction: function(target) {
        console.println("\n=== Annotation Text Diagnostics ===");
        
        try {
            let annotations = target.getAnnots();
            console.println("📝 Total annotations: " + annotations.length);
            
            if (annotations.length === 0) {
                console.println("ℹ️  No annotations found in document");
                return true;
            }
            
            for (let i = 0; i < Math.min(annotations.length, 5); i++) {
                let ann = annotations[i];
                console.println("\n--- Annotation " + (i + 1) + " ---");
                console.println("Type: " + (ann.type || "unknown"));
                
                // Check rich contents
                if (ann.richContents && ann.richContents.length > 0) {
                    console.println("📄 Rich contents found: " + ann.richContents.length + " elements");
                    for (let j = 0; j < ann.richContents.length; j++) {
                        if (ann.richContents[j] && ann.richContents[j].text) {
                            let text = ann.richContents[j].text;
                            console.println("📝 Rich text " + j + ": " + text.substring(0, 100));
                            
                            // Check for Chinese characters
                            let chineseChars = text.match(/[\u4e00-\u9fff]/g);
                            if (chineseChars) {
                                console.println("🈳 Chinese characters: " + chineseChars.length);
                            }
                        }
                    }
                } else if (ann.contents) {
                    console.println("📝 Plain contents: " + ann.contents.substring(0, 100));
                    
                    // Check for Chinese characters
                    let chineseChars = ann.contents.match(/[\u4e00-\u9fff]/g);
                    if (chineseChars) {
                        console.println("🈳 Chinese characters: " + chineseChars.length);
                    }
                } else {
                    console.println("❌ No text content found");
                }
            }
        } catch (e) {
            console.println("❌ Annotation extraction error: " + e.message);
        }
        
        return true;
    },
    
    // Test specific regex pattern
    testRegexPattern: function(pattern, testStrings) {
        console.println("\n=== Regex Pattern Testing ===");
        console.println("🔍 Pattern: " + pattern);
        
        try {
            let regex = new RegExp(pattern, "gi");
            console.println("✅ Pattern compiled successfully");
            
            if (testStrings && testStrings.length > 0) {
                for (let i = 0; i < testStrings.length; i++) {
                    let testString = testStrings[i];
                    let matches = testString.match(regex);
                    
                    console.println("\n--- Test String " + (i + 1) + " ---");
                    console.println("📝 Text: " + testString);
                    console.println("🎯 Matches: " + (matches ? matches.length : 0));
                    
                    if (matches) {
                        for (let j = 0; j < Math.min(matches.length, 3); j++) {
                            console.println("  Match " + (j + 1) + ": " + matches[j]);
                        }
                    }
                }
            } else {
                console.println("ℹ️  No test strings provided");
            }
            
        } catch (e) {
            console.println("❌ Pattern compilation error: " + e.message);
            return false;
        }
        
        return true;
    },
    
    // Test Unicode handling
    testUnicodeHandling: function() {
        console.println("\n=== Unicode Handling Test ===");
        
        let testStrings = [
            "中国工商银行",
            "中国工商银行 ******** 23-11-15 14:30:25 ABC123DEF456",
            "测试 Test 123",
            "English only text",
            "Mixed 中文 and English"
        ];
        
        for (let i = 0; i < testStrings.length; i++) {
            let str = testStrings[i];
            console.println("\n--- Unicode Test " + (i + 1) + " ---");
            console.println("📝 Original: " + str);
            console.println("📏 Length: " + str.length);
            console.println("🔢 Char codes: " + str.split('').slice(0, 10).map(c => c.charCodeAt(0)).join(', '));
            
            // Test Chinese character detection
            let chineseChars = str.match(/[\u4e00-\u9fff]/g);
            console.println("🈳 Chinese chars: " + (chineseChars ? chineseChars.length : 0));
            
            // Test regex matching
            let bankPattern = /中国工商银行/g;
            let matches = str.match(bankPattern);
            console.println("🏦 Bank pattern matches: " + (matches ? matches.length : 0));
        }
        
        return true;
    },
    
    // Comprehensive diagnostic run
    runFullDiagnostics: function(target) {
        console.println("🔧 Starting comprehensive diagnostics...");
        console.println("Time: " + new Date().toLocaleString());
        
        // Test Unicode handling first
        this.testUnicodeHandling();
        
        // Test regex pattern
        let testPattern = "中国工商银行\\s+\\d{8}\\s+\\d{2}-\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}\\s+[a-zA-Z0-9]{12}";
        let testStrings = [
            "中国工商银行 ******** 23-11-15 14:30:25 ABC123DEF456",
            "中国工商银行 ******** 24-01-20 09:15:30 XYZ789GHI012"
        ];
        this.testRegexPattern(testPattern, testStrings);
        
        // Test text extraction if target available
        if (target) {
            this.testTextExtraction(target);
            this.testAnnotationExtraction(target);
        } else {
            console.println("⚠️  No document target provided - skipping text extraction tests");
            console.println("💡 To run with document: regexDiagnostics.runFullDiagnostics(this)");
        }
        
        console.println("\n🏁 Diagnostics complete!");
        console.println("📋 Check the output above for any issues or limitations");
        
        return true;
    }
};

// Auto-load message
console.println("🔧 Regex Diagnostics Helper loaded");
console.println("📋 Available functions:");
console.println("  - regexDiagnostics.runFullDiagnostics(this) - Run all tests");
console.println("  - regexDiagnostics.testUnicodeHandling() - Test Unicode support");
console.println("  - regexDiagnostics.testRegexPattern(pattern, testStrings) - Test specific pattern");
console.println("💡 Open a PDF document and run: regexDiagnostics.runFullDiagnostics(this)");
