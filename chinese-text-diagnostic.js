/*
 * Chinese Text Diagnostic Tool for PDF-XChange Editor
 * Specifically designed to diagnose Chinese banking transaction text extraction issues
 */

var chineseTextDiagnostic = {
    
    // Test if PDF contains selectable text
    testTextSelectability: function(target) {
        console.println("=== PDF Text Selectability Test ===");
        
        try {
            // Check document properties
            console.println("Document Title: " + (target.title || "N/A"));
            console.println("Document Author: " + (target.author || "N/A"));
            console.println("Document Creator: " + (target.creator || "N/A"));
            console.println("Document Producer: " + (target.producer || "N/A"));
            console.println("Number of Pages: " + target.numPages);
            
            // Check if document has security restrictions
            if (target.security) {
                console.println("Security Settings:");
                console.println("  Print: " + target.security.canPrint);
                console.println("  Copy: " + target.security.canCopy);
                console.println("  Modify: " + target.security.canModify);
                console.println("  Extract: " + target.security.canExtract);
            }
            
            // Test basic text extraction capabilities
            console.println("\n--- Text Extraction API Test ---");
            let apiMethods = [
                'getPageNumWords',
                'getPageNthWord', 
                'getPageText',
                'extractText',
                'getPageContent',
                'getText',
                'getAnnots',
                'getPageAnnots'
            ];
            
            for (let method of apiMethods) {
                console.println(method + ": " + (typeof target[method] !== 'undefined' ? "✅ Available" : "❌ Not Available"));
            }
            
        } catch (e) {
            console.println("Error testing document properties: " + e.message);
        }
        
        return true;
    },
    
    // Comprehensive Chinese text extraction test
    testChineseTextExtraction: function(target) {
        console.println("\n=== Chinese Text Extraction Analysis ===");
        
        if (!target || !target.numPages) {
            console.println("❌ No valid document target");
            return false;
        }
        
        let results = {
            totalPages: target.numPages,
            pagesWithWords: 0,
            totalWords: 0,
            nonEmptyWords: 0,
            chineseWords: 0,
            whitespaceOnlyWords: 0,
            extractionMethods: {},
            sampleTexts: []
        };
        
        // Test first 3 pages in detail
        let pagesToTest = Math.min(3, target.numPages);
        
        for (let pageNum = 0; pageNum < pagesToTest; pageNum++) {
            console.println("\n--- Analyzing Page " + (pageNum + 1) + " ---");
            
            try {
                // Method 1: Word-by-word analysis
                if (typeof target.getPageNumWords !== 'undefined' && typeof target.getPageNthWord !== 'undefined') {
                    let numWords = target.getPageNumWords(pageNum);
                    results.totalWords += numWords;
                    
                    console.println("Reported word count: " + numWords);
                    
                    if (numWords > 0) {
                        results.pagesWithWords++;
                        let pageAnalysis = {
                            pageNum: pageNum + 1,
                            words: [],
                            chineseCount: 0,
                            emptyCount: 0,
                            whitespaceCount: 0
                        };
                        
                        // Analyze each word in detail
                        for (let wordIndex = 0; wordIndex < Math.min(numWords, 50); wordIndex++) {
                            try {
                                let word = target.getPageNthWord(pageNum, wordIndex);
                                
                                let wordAnalysis = {
                                    index: wordIndex,
                                    text: word,
                                    length: word ? word.length : 0,
                                    isEmpty: !word || word.length === 0,
                                    isWhitespace: word ? /^\s*$/.test(word) : true,
                                    hasChinese: word ? /[\u4e00-\u9fff]/.test(word) : false,
                                    charCodes: word ? word.split('').map(c => c.charCodeAt(0)) : [],
                                    unicodeRanges: word ? this.analyzeUnicodeRanges(word) : {}
                                };
                                
                                pageAnalysis.words.push(wordAnalysis);
                                
                                if (wordAnalysis.isEmpty) {
                                    pageAnalysis.emptyCount++;
                                    results.whitespaceOnlyWords++;
                                } else if (wordAnalysis.isWhitespace) {
                                    pageAnalysis.whitespaceCount++;
                                    results.whitespaceOnlyWords++;
                                } else {
                                    results.nonEmptyWords++;
                                    if (wordAnalysis.hasChinese) {
                                        pageAnalysis.chineseCount++;
                                        results.chineseWords++;
                                    }
                                }
                                
                            } catch (e) {
                                console.println("Error extracting word " + wordIndex + ": " + e.message);
                            }
                        }
                        
                        // Report page analysis
                        console.println("Page " + (pageNum + 1) + " Analysis:");
                        console.println("  Total words analyzed: " + pageAnalysis.words.length);
                        console.println("  Empty words: " + pageAnalysis.emptyCount);
                        console.println("  Whitespace-only words: " + pageAnalysis.whitespaceCount);
                        console.println("  Words with Chinese: " + pageAnalysis.chineseCount);
                        console.println("  Non-empty words: " + (pageAnalysis.words.length - pageAnalysis.emptyCount - pageAnalysis.whitespaceCount));
                        
                        // Show sample words
                        console.println("  Sample words (first 10):");
                        for (let i = 0; i < Math.min(10, pageAnalysis.words.length); i++) {
                            let w = pageAnalysis.words[i];
                            console.println("    [" + i + "] '" + w.text + "' (len:" + w.length + 
                                          ", empty:" + w.isEmpty + ", ws:" + w.isWhitespace + 
                                          ", chinese:" + w.hasChinese + ")");
                            if (w.charCodes.length > 0) {
                                console.println("        Char codes: " + w.charCodes.slice(0, 10).join(','));
                            }
                        }
                        
                        // Store sample for overall analysis
                        if (pageAnalysis.chineseCount > 0) {
                            let chineseWords = pageAnalysis.words.filter(w => w.hasChinese);
                            results.sampleTexts.push({
                                page: pageNum + 1,
                                chineseWords: chineseWords.slice(0, 5).map(w => w.text)
                            });
                        }
                    }
                }
                
                // Method 2: Try alternative extraction methods
                let alternativeMethods = [
                    { name: "getPageText", func: () => target.getPageText(pageNum) },
                    { name: "extractText", func: () => target.extractText(pageNum) }
                ];
                
                for (let method of alternativeMethods) {
                    try {
                        if (typeof method.func === 'function') {
                            let result = method.func();
                            if (result) {
                                results.extractionMethods[method.name] = {
                                    success: true,
                                    length: result.length,
                                    hasChinese: /[\u4e00-\u9fff]/.test(result),
                                    sample: result.substring(0, 100)
                                };
                                console.println(method.name + " result: " + result.length + " chars, Chinese: " + 
                                              /[\u4e00-\u9fff]/.test(result));
                            } else {
                                results.extractionMethods[method.name] = { success: false, error: "No result" };
                            }
                        }
                    } catch (e) {
                        results.extractionMethods[method.name] = { success: false, error: e.message };
                    }
                }
                
            } catch (e) {
                console.println("Error analyzing page " + (pageNum + 1) + ": " + e.message);
            }
        }
        
        // Final summary
        console.println("\n=== Chinese Text Extraction Summary ===");
        console.println("Total pages tested: " + pagesToTest);
        console.println("Pages with words: " + results.pagesWithWords);
        console.println("Total words reported: " + results.totalWords);
        console.println("Non-empty words: " + results.nonEmptyWords);
        console.println("Chinese words found: " + results.chineseWords);
        console.println("Whitespace-only words: " + results.whitespaceOnlyWords);
        
        console.println("\nExtraction Methods Results:");
        for (let method in results.extractionMethods) {
            let result = results.extractionMethods[method];
            if (result.success) {
                console.println("  " + method + ": ✅ " + result.length + " chars, Chinese: " + result.hasChinese);
                if (result.sample) {
                    console.println("    Sample: '" + result.sample + "'");
                }
            } else {
                console.println("  " + method + ": ❌ " + result.error);
            }
        }
        
        if (results.sampleTexts.length > 0) {
            console.println("\nChinese Text Samples Found:");
            for (let sample of results.sampleTexts) {
                console.println("  Page " + sample.page + ": " + sample.chineseWords.join(", "));
            }
        }
        
        // Diagnosis
        console.println("\n=== DIAGNOSIS ===");
        if (results.chineseWords === 0 && results.totalWords > 0) {
            console.println("🚨 ISSUE: Document has " + results.totalWords + " words but NO Chinese characters detected");
            console.println("This suggests:");
            console.println("1. Text is stored as images/graphics, not searchable text");
            console.println("2. Chinese characters are encoded in a way the API cannot read");
            console.println("3. Document has security restrictions preventing text access");
            console.println("4. PDF structure is incompatible with JavaScript API");
        } else if (results.chineseWords > 0) {
            console.println("✅ SUCCESS: Found " + results.chineseWords + " Chinese words");
            console.println("The plugin should be able to search Chinese text in this document");
        } else if (results.totalWords === 0) {
            console.println("🚨 CRITICAL: No words detected at all");
            console.println("This document may be:");
            console.println("1. A scanned image without OCR");
            console.println("2. Protected with security settings");
            console.println("3. Corrupted or incompatible format");
        }
        
        return results;
    },
    
    // Analyze Unicode character ranges in text
    analyzeUnicodeRanges: function(text) {
        let ranges = {
            ascii: 0,
            latin: 0,
            chinese: 0,
            other: 0
        };
        
        for (let char of text) {
            let code = char.charCodeAt(0);
            if (code < 128) {
                ranges.ascii++;
            } else if (code < 256) {
                ranges.latin++;
            } else if (code >= 0x4e00 && code <= 0x9fff) {
                ranges.chinese++;
            } else {
                ranges.other++;
            }
        }
        
        return ranges;
    },
    
    // Test specific banking pattern
    testBankingPattern: function(target) {
        console.println("\n=== Banking Pattern Test ===");
        
        let testPattern = /中国工商银行\s+\d{8}\s+\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\s+[a-zA-Z0-9]{12}/g;
        let testStrings = [
            "中国工商银行 ******** 23-11-15 14:30:25 ABC123DEF456",
            "中国工商银行 ******** 24-01-20 09:15:30 XYZ789GHI012"
        ];
        
        console.println("Pattern: " + testPattern.toString());
        console.println("Testing with sample strings:");
        
        for (let i = 0; i < testStrings.length; i++) {
            let testString = testStrings[i];
            let matches = testString.match(testPattern);
            console.println("Test " + (i + 1) + ": '" + testString + "'");
            console.println("  Matches: " + (matches ? matches.length : 0));
            if (matches) {
                console.println("  Result: " + matches[0]);
            }
        }
        
        return true;
    },
    
    // Run complete diagnostic
    runCompleteDiagnostic: function(target) {
        console.println("🔍 Starting Complete Chinese Text Diagnostic");
        console.println("Time: " + new Date().toLocaleString());
        console.println("==========================================");
        
        this.testTextSelectability(target);
        this.testChineseTextExtraction(target);
        this.testBankingPattern(target);
        
        console.println("\n==========================================");
        console.println("🏁 Diagnostic Complete!");
        console.println("Check the detailed output above to identify the specific issue.");
        
        return true;
    }
};

// Auto-load message
console.println("🈳 Chinese Text Diagnostic Tool loaded");
console.println("📋 Usage: chineseTextDiagnostic.runCompleteDiagnostic(this)");
console.println("💡 Open your PDF with Chinese banking text and run the diagnostic");
