# Installation Guide - Enhanced Regex Find/Replace Plugin

## Quick Installation

### Step 1: Locate JavaScript Folder
1. Open PDF-XChange Editor
2. Navigate to the JavaScript folder:
   - **Windows**: `%APPDATA%\Tracker Software\PDF-XChange Editor\JavaScripts`
   - **Alternative**: `C:\Users\<USER>\AppData\Roaming\Tracker Software\PDF-XChange Editor\JavaScripts`

### Step 2: Install Plugin
1. Copy `regex-find-replace-plugin.js` to the JavaScripts folder
2. If the folder doesn't exist, create it manually

### Step 3: Enable JavaScript
1. In PDF-XChange Editor, go to `Edit > Preferences`
2. Navigate to `JavaScript` in the left panel
3. Check "Enable JavaScript"
4. Click "OK"

### Step 4: Restart Application
1. Close PDF-XChange Editor completely
2. Restart the application
3. Verify installation by checking for "Regex Find/Replace…" in the Edit menu

## Detailed Installation Instructions

### Finding the JavaScript Directory

#### Method 1: Using Windows Explorer
1. Press `Windows + R` to open Run dialog
2. Type `%APPDATA%` and press Enter
3. Navigate to `Tracker Software\PDF-XChange Editor\JavaScripts`
4. If the `JavaScripts` folder doesn't exist, create it

#### Method 2: Through PDF-XChange Editor
1. Open PDF-XChange Editor
2. Go to `Help > About`
3. Look for installation directory information
4. Navigate to that directory and find/create the `JavaScripts` subfolder

### JavaScript Security Settings

#### Enable JavaScript Execution
1. Open `Edit > Preferences`
2. Select `JavaScript` from the left panel
3. Ensure these settings are configured:
   - ✅ "Enable JavaScript"
   - ✅ "Enable menu items from JavaScript"
   - ✅ "Enable toolbar buttons from JavaScript"

#### Global Object Security (Optional)
If you encounter global variable errors:
1. In JavaScript preferences, uncheck "Enable global object security policy"
2. **OR** manually edit the GlobData file (advanced users only)

### Verification Steps

#### Check Menu Integration
1. Open any PDF document
2. Go to `Edit` menu
3. Look for "Regex Find/Replace…" menu item
4. It should appear after the standard "Find..." option

#### Check Toolbar Integration
1. Look for the regex icon in the toolbar
2. Hover over it to see "Regex Find and Replace" tooltip
3. The icon should be clickable when a document is open

#### Test Basic Functionality
1. Open a PDF with text content
2. Click the regex find/replace menu item or toolbar button
3. The enhanced dialog should appear
4. Try a simple text replacement to verify functionality

## Troubleshooting Installation

### Plugin Not Loading

#### Check File Location
- Ensure `regex-find-replace-plugin.js` is in the correct JavaScripts folder
- Verify the file has `.js` extension (not `.js.txt`)
- Check file permissions - ensure it's readable

#### Verify JavaScript Settings
- Confirm JavaScript is enabled in preferences
- Check that menu items and toolbar buttons are allowed
- Restart PDF-XChange Editor after changing settings

#### Console Errors
1. Open JavaScript Console: `View > Panes > JavaScript Console`
2. Look for error messages during startup
3. Common errors and solutions:
   - **Syntax Error**: Check file integrity, re-download if necessary
   - **Permission Error**: Run PDF-XChange Editor as administrator
   - **Global Variable Error**: Adjust security settings as described above

### Menu Item Missing

#### Refresh Menu
1. Close all PDF documents
2. Restart PDF-XChange Editor
3. Open a new document
4. Check Edit menu again

#### Manual Menu Position
If the menu item appears in wrong position:
1. Edit the plugin file
2. Modify the `nPos` value in the `app.addMenuItem` call
3. Save and restart PDF-XChange Editor

### Toolbar Button Issues

#### Button Not Visible
1. Right-click on toolbar
2. Select "Customize Toolbar"
3. Look for "Regex Find and Replace" in available buttons
4. Drag it to desired position

#### Button Disabled
- Ensure a PDF document is open
- Check that the document contains text content
- Verify JavaScript permissions

## Advanced Configuration

### Custom Installation Path
If using a portable version or custom installation:
1. Locate your PDF-XChange Editor installation directory
2. Find or create a `JavaScripts` subfolder
3. Place the plugin file there
4. Adjust preferences to point to custom JavaScript directory if needed

### Network/Corporate Environments
For corporate installations:
1. Check with IT department about JavaScript policies
2. Ensure network drives allow script execution
3. Consider deploying via group policy if needed
4. Test with local administrator privileges first

### Multiple User Setup
For shared computers:
1. Install in system-wide JavaScript directory if available
2. Or install separately for each user profile
3. Ensure all users have appropriate permissions

## Uninstallation

### Remove Plugin
1. Navigate to the JavaScripts folder
2. Delete `regex-find-replace-plugin.js`
3. Restart PDF-XChange Editor

### Clean Up Settings
1. The plugin stores settings in global variables
2. These will be automatically cleaned up over time
3. For immediate cleanup, reset JavaScript preferences

## Version Compatibility

### Supported Versions
- PDF-XChange Editor v8.0 and later
- PDF-XChange Editor Pro
- PDF-XChange Editor Plus

### Feature Limitations
Some features may be limited based on:
- PDF-XChange Editor version
- JavaScript API availability
- Document security settings
- PDF structure complexity

## Getting Help

### Log Files
Enable JavaScript console logging:
1. `View > Panes > JavaScript Console`
2. Monitor for error messages
3. Copy relevant errors for troubleshooting

### Common Solutions
- **Restart PDF-XChange Editor** after installation
- **Run as Administrator** if permission issues occur
- **Check file integrity** if syntax errors appear
- **Update PDF-XChange Editor** for best compatibility

### Support Resources
- PDF-XChange Editor documentation
- JavaScript API reference
- Community forums
- Plugin README.md file for detailed usage instructions
