# Enhanced Regex Find/Replace Plugin for PDF-XChange Editor

## Overview

This JavaScript plugin provides advanced regular expression find and replace functionality for PDF-XChange Editor. It extends the basic find/replace capabilities with powerful regex support, batch processing, and comprehensive text manipulation features.

## Features

### Core Functionality
- **Regular Expression Support**: Full JavaScript regex support with validation
- **Literal Text Search**: Traditional find/replace with automatic regex escaping
- **Case Sensitivity Control**: Toggle case-sensitive/insensitive matching
- **Multi-line Support**: Handle text spanning multiple lines
- **Dot-All Mode**: Make `.` match newline characters

### Search Scope Options
- **Annotations Only**: Search and replace within PDF annotations
- **Document Text**: Process the actual document text content
- **Both**: Comprehensive search across annotations and document text

### Advanced Features
- **Batch Processing**: Execute multiple find/replace operations in sequence
- **Pattern Validation**: Real-time regex pattern validation with error reporting
- **Search History**: Automatic saving of recent search patterns and replacements
- **Detailed Statistics**: Comprehensive reporting of replacement operations
- **Error Handling**: Robust error handling with user-friendly messages

## Installation

1. **Download the Plugin**
   - Save `regex-find-replace-plugin.js` to your PDF-XChange Editor JavaScript folder
   - Default location: `%APPDATA%\Tracker Software\PDF-XChange Editor\JavaScripts`

2. **Enable JavaScript**
   - Open PDF-XChange Editor
   - Go to `Edit > Preferences > JavaScript`
   - Ensure "Enable JavaScript" is checked
   - Restart PDF-XChange Editor

3. **Verify Installation**
   - Look for "Regex Find/Replace…" in the Edit menu
   - Check for the regex icon in the toolbar

## Usage

### Basic Find/Replace

1. **Open the Dialog**
   - Click `Edit > Regex Find/Replace…` or use the toolbar button
   - The enhanced find/replace dialog will appear

2. **Configure Search**
   - **Find Pattern**: Enter your search text or regex pattern
   - **Replace With**: Enter replacement text (supports regex groups like `$1`, `$2`)
   - **Use Regular Expressions**: Check to enable regex mode
   - **Case Sensitive**: Check for case-sensitive matching

3. **Set Search Scope**
   - **Both**: Search annotations and document text (default)
   - **Annotations**: Search only PDF annotations
   - **Document**: Search only document text content

4. **Execute**
   - Click "Execute" to perform the replacement
   - View detailed results in the popup dialog

### Regular Expression Examples

#### Basic Patterns
```javascript
// Find email addresses
\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b

// Find phone numbers
\b\d{3}[-.]?\d{3}[-.]?\d{4}\b

// Find dates (MM/DD/YYYY format)
\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b

// Find URLs
https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b
```

#### Advanced Patterns with Replacement
```javascript
// Convert dates from MM/DD/YYYY to DD-MM-YYYY
Find: (\d{1,2})[\/](\d{1,2})[\/](\d{4})
Replace: $2-$1-$3

// Add prefix to email addresses
Find: \b([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})\b
Replace: mailto:$1

// Format phone numbers consistently
Find: \b(\d{3})[-.]?(\d{3})[-.]?(\d{4})\b
Replace: ($1) $2-$3
```

### Batch Processing

Batch mode allows you to execute multiple find/replace operations in sequence.

1. **Enable Batch Mode**
   - Check "Enable Batch Mode" in the dialog
   - The batch operations text area will become active

2. **Define Operations**
   - Enter operations as JSON array in the text area
   - Each operation can have its own settings

#### Batch Operation Format
```json
[
  {
    "find": "pattern1",
    "replace": "replacement1",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "pattern2",
    "replace": "replacement2",
    "useRegex": false,
    "caseSensitive": true
  }
]
```

#### Example Batch Operations
```json
[
  {
    "find": "\\b(\\d{1,2})[\/](\\d{1,2})[\/](\\d{4})\\b",
    "replace": "$2-$1-$3",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "\\b([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,})\\b",
    "replace": "mailto:$1",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "old company name",
    "replace": "New Company Name",
    "useRegex": false,
    "caseSensitive": false
  }
]
```

## Configuration Options

### Regex Flags
- **Global (g)**: Always enabled - finds all matches
- **Case Insensitive (i)**: Controlled by "Case Sensitive" checkbox
- **Multiline (m)**: Controlled by "Multiline Mode" checkbox
- **Dot All (s)**: Controlled by "Dot Matches All" checkbox

### Search Scope Details
- **Annotations**: Processes both rich text and plain text annotations
- **Document**: Attempts to process actual PDF text content
- **Both**: Comprehensive search across all text elements

### History and Persistence
- Automatically saves the last 15 search patterns and replacements
- Settings persist between PDF-XChange Editor sessions
- Dropdown menus provide quick access to recent searches

## Error Handling

The plugin includes comprehensive error handling:

- **Regex Validation**: Invalid patterns are caught before execution
- **Batch Operation Validation**: JSON syntax and structure validation
- **API Error Handling**: Graceful handling of PDF-XChange Editor API limitations
- **User Feedback**: Clear error messages and warnings

## Limitations

### Document Text Processing
- Direct document text replacement may be limited by PDF-XChange Editor's JavaScript API
- Some PDF documents may not support text extraction/modification
- Complex PDF structures (forms, embedded objects) may not be fully supported

### Performance Considerations
- Large documents may take longer to process
- Complex regex patterns can impact performance
- Batch operations are limited to 10 operations per execution

## Troubleshooting

### Common Issues

1. **Plugin Not Loading**
   - Verify JavaScript is enabled in preferences
   - Check file location and permissions
   - Restart PDF-XChange Editor

2. **Regex Errors**
   - Use the built-in validation to check patterns
   - Test patterns in a regex testing tool first
   - Remember to escape special characters in JSON

3. **No Matches Found**
   - Verify search scope settings
   - Check case sensitivity settings
   - Test with simpler patterns first

4. **Global Variable Errors**
   - Disable "Enable global object security policy" in preferences
   - Or manually edit the GlobData file to remove conflicts

### Debug Information
The plugin logs detailed information to the JavaScript console:
- Pattern validation results
- Processing statistics
- Error messages and warnings

## Version History

- **v2.0.0** (September 2025): Complete rewrite with enhanced features
- **v1.2.1** (March 2024): Original annotation-only version

## Support

For issues and feature requests, refer to the PDF-XChange Editor JavaScript documentation and community forums.
