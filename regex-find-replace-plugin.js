/* 
 * Enhanced Regular Expression Find and Replace Plugin for PDF-XChange Editor
 * Version: 2.0.0
 * Date: September 25, 2025
 * 
 * Features:
 * - Regular expression find and replace for document text and annotations
 * - Batch processing with multiple find/replace operations
 * - Advanced regex pattern validation
 * - Comprehensive error handling
 * - User feedback with detailed replacement statistics
 * - Support for multi-line text and special characters
 * - Case-sensitive and case-insensitive matching
 * - Backup and restore functionality
 */

// Add menu item for the enhanced find/replace functionality
app.addMenuItem({
    cName: "regexFindReplace",
    cUser: "Regex Find/Replace…",
    cParent: "Edit",
    nPos: 22,
    cExec: 'regexFindReplace.run(this)'
});

// Custom icon for the toolbar button
var regexIcon = {
    count: 0, 
    width: 20, 
    height: 20, 
    read: function(nBytes = this.data.length/2) {
        return this.data.slice(this.count, this.count += 2*nBytes);
    }, 
    data: "0000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF808080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF868686FF808080FF808080FF8080800000000000000000FF808080FF7F7F7F000000000000000000000000000000000000000000000000FF808080FF808080FF8080800000000000000000000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF89898900000000000000000000000000000000FF808080FF808080000000000000000000000000000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF8181810000000000000000FF808080FF808080000000000000000000000000000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF8080800000000000000000FF808080FF8080800000000000000000000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF8080800000000000000000FF808080FF8080800000000000000000000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF808080000000000000000000000000FF808080FF80808000000000000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF80808000000000000000000000000000000000FF808080FF808080000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF8080800000000000000000000000000000000000000000FF808080FF8080800000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF8080800000000000000000FF808080000000000000000000000000FF808080FF80808000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF8080800000000000000000FF808080FF808080000000000000000000000000FF808080FF808080FF8080800000000000000000FF808080FF808080FF808080FF808080FF838383FF808080FF808080000000000000000000000000FF808080FF80808000000000000000000000000000000000FF8080800000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF808080000000000000000000000000FF808080FF808080FF808080000000000000000000000000000000000000000000000000FF808080FF808080FF808080FF808080FF808080FF808080FF808080000000000000000000000000FF808080FF808080FF808080000000000000000000000000000000000000000000000000FF808080FF8080800000000000000000FF808080FF808080FF808080000000000000000000000000FF808080FF808080FF808080FF808080000000000000000000000000000000000000000000000000FF808080000000000000000000000000FF808080FF808080FF80808000000000FF808080FF808080FF808080FF808080FF808080FF808080FF80808000000000000000000000000000000000FF808080FF8080800000000000000000FF808080FF8080800000000000000000FF808080FF808080FF808080000000000000000000000000FF808080FF808080FF808080000000000000000000000000FF808080FF808080FF808080FF808080FF80808000000000000000000000000000000000000000000000000000000000000000000000000000000000FF808080FF808080FF8080800000000000000000FF808080FF808080FF80808000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000FF808080FF808080000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
};

// Add toolbar button
app.addToolButton({
    cName: "regexFindReplace",
    oIcon: regexIcon,
    cLabel: "",
    cExec: 'regexFindReplace.run(this)',
    cTooltext: "Regex Find and Replace",
    cEnable: "event.rc = (event.target != null);",
    nPos: -1
});

// Main plugin object
var regexFindReplace = {
    SAVE_LENGTH: 15, // Number of search terms to save
    MAX_BATCH_OPERATIONS: 10, // Maximum batch operations
    
    // Regex validation utility
    validateRegex: function(pattern, flags) {
        try {
            new RegExp(pattern, flags);
            return { valid: true, error: null };
        } catch (e) {
            return { valid: false, error: e.message };
        }
    },
    
    // Escape special regex characters for literal search
    escapeRegex: function(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    },
    
    // Dialog configuration
    dialog: {
        data: {},
        initialize: function(dialog) {
            let def = this.data;
            
            if (!Object.keys(def).length) {
                def = {
                    "findText": "",
                    "replaceText": "",
                    "useRegex": true,
                    "caseSensitive": false,
                    "multiline": false,
                    "dotAll": false,
                    "searchScope": "both", // "annotations", "document", "both"
                    "batchMode": false,
                    "batchOperations": ""
                };
            } else {
                ["findText", "replaceText"].forEach(i => def[i] = this.dd(def[i]));
            }
            dialog.load(def);
        },
        
        commit: function(dialog) {
            this.data = dialog.store();
        },
        
        // Create dropdown object for history
        dd: function(arr) {
            let ob = { "": 1 }; // blank as selected item
            if (!Array.isArray(arr))
                arr = [arr];
            for (let i in arr) {
                ob[arr[i]] = -2 - i;
            }
            return ob;
        },
        
        description: {
            name: "Enhanced Regex Find & Replace",
            align_children: "align_left",
            elements: [
                {
                    type: "cluster",
                    name: "Search and Replace Configuration",
                    align_children: "align_left",
                    elements: [
                        {
                            type: "view",
                            align_children: "align_row",
                            elements: [
                                {
                                    type: "static_text",
                                    name: "Find Pattern:",
                                    alignment: "align_right",
                                    width: 80
                                },
                                {
                                    item_id: "findText",
                                    type: "edit_text",
                                    PopupEdit: true,
                                    alignment: "align_left",
                                    width: 350,
                                    height: 60
                                }
                            ]
                        },
                        {
                            type: "view",
                            align_children: "align_row",
                            elements: [
                                {
                                    type: "static_text",
                                    name: "Replace With:",
                                    alignment: "align_right",
                                    width: 80
                                },
                                {
                                    item_id: "replaceText",
                                    type: "edit_text",
                                    PopupEdit: true,
                                    alignment: "align_left",
                                    width: 350,
                                    height: 60
                                }
                            ]
                        }
                    ]
                },
                {
                    type: "cluster",
                    name: "Options",
                    align_children: "align_left",
                    elements: [
                        {
                            type: "view",
                            align_children: "align_row",
                            elements: [
                                {
                                    type: "check_box",
                                    name: "Use Regular Expressions",
                                    item_id: "useRegex"
                                },
                                {
                                    type: "check_box",
                                    name: "Case Sensitive",
                                    item_id: "caseSensitive"
                                }
                            ]
                        },
                        {
                            type: "view",
                            align_children: "align_row",
                            elements: [
                                {
                                    type: "check_box",
                                    name: "Multiline Mode",
                                    item_id: "multiline"
                                },
                                {
                                    type: "check_box",
                                    name: "Dot Matches All",
                                    item_id: "dotAll"
                                }
                            ]
                        },
                        {
                            type: "view",
                            align_children: "align_row",
                            elements: [
                                {
                                    type: "static_text",
                                    name: "Search Scope:",
                                    width: 80
                                },
                                {
                                    type: "popup",
                                    item_id: "searchScope",
                                    width: 120,
                                    alignment: "align_left"
                                }
                            ]
                        }
                    ]
                },
                {
                    type: "cluster",
                    name: "Batch Processing",
                    align_children: "align_left",
                    elements: [
                        {
                            type: "check_box",
                            name: "Enable Batch Mode",
                            item_id: "batchMode"
                        },
                        {
                            type: "view",
                            align_children: "align_row",
                            elements: [
                                {
                                    type: "static_text",
                                    name: "Batch Operations (JSON):",
                                    alignment: "align_right",
                                    width: 150
                                },
                                {
                                    item_id: "batchOperations",
                                    type: "edit_text",
                                    PopupEdit: true,
                                    alignment: "align_left",
                                    width: 300,
                                    height: 80
                                }
                            ]
                        }
                    ]
                },
                {
                    alignment: "align_right",
                    type: "ok_cancel",
                    ok_name: "Execute",
                    cancel_name: "Cancel"
                }
            ]
        }
    },

    // Main execution function
    run: function(target) {
        // Load saved settings
        let globals = this.global.get();
        Object.assign(this.dialog.data, globals);

        // Setup search scope dropdown - fix UI display issue
        if (!this.dialog.data.searchScope || typeof this.dialog.data.searchScope === 'object') {
            this.dialog.data.searchScope = "both"; // Set default as string
        }

        // Ensure dropdown options are properly configured
        this.setupSearchScopeDropdown();

        if ("ok" == app.execDialog(this.dialog)) {
            try {
                let results = this.dialog.data;
                let output;

                if (results.batchMode && results.batchOperations) {
                    output = this.executeBatchOperations(target, results);
                } else {
                    output = this.executeSingleOperation(target, results);
                }

                console.println(output);
                app.alert(output, 3); // Show results to user

                // Save settings
                this.global.set(this.mkArrays(globals));

            } catch (error) {
                let errorMsg = "Error during find/replace operation: " + error.message;
                console.println(errorMsg);
                app.alert(errorMsg, 0); // Error alert
            }
        } else {
            return "User Cancelled";
        }
    },

    // Execute single find/replace operation
    executeSingleOperation: function(target, config) {
        let findPattern = config.findText;
        let replaceText = config.replaceText;

        if (!findPattern) {
            return "Error: Find pattern cannot be empty";
        }

        // Validate regex if enabled
        if (config.useRegex) {
            let flags = this.buildRegexFlags(config);
            let validation = this.validateRegex(findPattern, flags);
            if (!validation.valid) {
                return "Error: Invalid regular expression - " + validation.error;
            }
        }

        let stats = {
            annotationReplacements: 0,
            documentReplacements: 0,
            totalMatches: 0,
            pagesProcessed: 0,
            annotationsProcessed: 0
        };

        // Process based on search scope
        if (config.searchScope === "annotations" || config.searchScope === "both") {
            stats.annotationReplacements = this.processAnnotations(target, findPattern, replaceText, config);
        }

        if (config.searchScope === "document" || config.searchScope === "both") {
            let docStats = this.processDocumentText(target, findPattern, replaceText, config);
            stats.documentReplacements = docStats.replacements;
            stats.pagesProcessed = docStats.pagesProcessed;
        }

        stats.totalMatches = stats.annotationReplacements + stats.documentReplacements;

        return this.formatResults(stats, config);
    },

    // Execute batch operations
    executeBatchOperations: function(target, config) {
        try {
            let batchOps = JSON.parse(config.batchOperations);
            if (!Array.isArray(batchOps)) {
                return "Error: Batch operations must be an array";
            }

            if (batchOps.length > this.MAX_BATCH_OPERATIONS) {
                return "Error: Maximum " + this.MAX_BATCH_OPERATIONS + " batch operations allowed";
            }

            let totalStats = {
                annotationReplacements: 0,
                documentReplacements: 0,
                totalMatches: 0,
                pagesProcessed: 0,
                operationsExecuted: 0
            };

            for (let i = 0; i < batchOps.length; i++) {
                let op = batchOps[i];
                if (!op.find || typeof op.find !== 'string') {
                    console.println("Warning: Skipping invalid batch operation " + (i + 1));
                    continue;
                }

                let opConfig = Object.assign({}, config, {
                    findText: op.find,
                    replaceText: op.replace || "",
                    useRegex: op.useRegex !== undefined ? op.useRegex : config.useRegex,
                    caseSensitive: op.caseSensitive !== undefined ? op.caseSensitive : config.caseSensitive
                });

                // Validate regex for this operation
                if (opConfig.useRegex) {
                    let flags = this.buildRegexFlags(opConfig);
                    let validation = this.validateRegex(opConfig.findText, flags);
                    if (!validation.valid) {
                        console.println("Warning: Skipping invalid regex in operation " + (i + 1) + ": " + validation.error);
                        continue;
                    }
                }

                let opStats = this.executeSingleOperationInternal(target, opConfig);
                totalStats.annotationReplacements += opStats.annotationReplacements;
                totalStats.documentReplacements += opStats.documentReplacements;
                totalStats.operationsExecuted++;
            }

            totalStats.totalMatches = totalStats.annotationReplacements + totalStats.documentReplacements;

            return "Batch Processing Complete:\n" +
                   "Operations executed: " + totalStats.operationsExecuted + "\n" +
                   "Total replacements: " + totalStats.totalMatches + "\n" +
                   "Annotation replacements: " + totalStats.annotationReplacements + "\n" +
                   "Document replacements: " + totalStats.documentReplacements;

        } catch (error) {
            return "Error parsing batch operations: " + error.message;
        }
    },

    // Internal single operation execution (for batch processing)
    executeSingleOperationInternal: function(target, config) {
        let stats = {
            annotationReplacements: 0,
            documentReplacements: 0
        };

        if (config.searchScope === "annotations" || config.searchScope === "both") {
            stats.annotationReplacements = this.processAnnotations(target, config.findText, config.replaceText, config);
        }

        if (config.searchScope === "document" || config.searchScope === "both") {
            let docStats = this.processDocumentText(target, config.findText, config.replaceText, config);
            stats.documentReplacements = docStats.replacements;
        }

        return stats;
    },

    // Build regex flags based on configuration
    buildRegexFlags: function(config) {
        let flags = "g"; // Always global
        if (!config.caseSensitive) flags += "i";
        if (config.multiline) flags += "m";
        if (config.dotAll) flags += "s";
        return flags;
    },

    // Setup search scope dropdown properly
    setupSearchScopeDropdown: function() {
        // This ensures the dropdown displays correctly
        if (!this.dialog.description) return;

        // Find the search scope popup element and configure it
        let elements = this.dialog.description.elements;
        for (let cluster of elements) {
            if (cluster.elements) {
                for (let element of cluster.elements) {
                    if (element.elements) {
                        for (let subElement of element.elements) {
                            if (subElement.item_id === "searchScope" && subElement.type === "popup") {
                                // Configure dropdown options properly
                                if (!subElement.options) {
                                    subElement.options = [
                                        ["both", "Both (Document + Annotations)"],
                                        ["document", "Document Text Only"],
                                        ["annotations", "Annotations Only"]
                                    ];
                                }
                            }
                        }
                    }
                }
            }
        }
    },

    // Process annotations for find/replace
    processAnnotations: function(target, findPattern, replaceText, config) {
        let replacements = 0;

        try {
            let annotations = [];

            // Try multiple methods to get annotations
            try {
                // Method 1: Selected annotations first
                if (target.selectedAnnots && target.selectedAnnots.length > 0) {
                    annotations = target.selectedAnnots;
                    console.println("Found " + annotations.length + " selected annotations");
                }
            } catch (e) {
                console.println("Cannot access selected annotations: " + e.message);
            }

            // Method 2: All annotations if none selected
            if (annotations.length === 0) {
                try {
                    if (target.getAnnots) {
                        annotations = target.getAnnots();
                        console.println("Found " + (annotations ? annotations.length : 0) + " total annotations");
                    }
                } catch (e) {
                    console.println("Cannot access document annotations: " + e.message);
                }
            }

            // Method 3: Try page-by-page annotation access
            if (!annotations || annotations.length === 0) {
                try {
                    annotations = [];
                    for (let pageNum = 0; pageNum < target.numPages; pageNum++) {
                        try {
                            if (target.getPageAnnots) {
                                let pageAnnots = target.getPageAnnots(pageNum);
                                if (pageAnnots && pageAnnots.length > 0) {
                                    annotations = annotations.concat(pageAnnots);
                                }
                            }
                        } catch (e) {
                            // Skip problematic pages
                        }
                    }
                    console.println("Found " + annotations.length + " annotations via page-by-page access");
                } catch (e) {
                    console.println("Page-by-page annotation access failed: " + e.message);
                }
            }

            if (!annotations || annotations.length === 0) {
                console.println("No annotations found or accessible in document");
                return 0;
            }

            // Build regex or literal pattern
            let searchPattern;
            if (config.useRegex) {
                let flags = this.buildRegexFlags(config);
                searchPattern = new RegExp(findPattern, flags);
            } else {
                let flags = this.buildRegexFlags(config);
                searchPattern = new RegExp(this.escapeRegex(findPattern), flags);
            }

            // Process each annotation
            for (let ann of annotations) {
                let changed = false;

                // Handle rich contents (formatted text)
                if (ann.richContents && ann.richContents.length > 0) {
                    for (let i = 0; i < ann.richContents.length; i++) {
                        if (ann.richContents[i] && ann.richContents[i].text) {
                            let originalText = ann.richContents[i].text;
                            let newText = originalText.replace(searchPattern, replaceText);

                            if (newText !== originalText) {
                                ann.richContents[i].text = newText;
                                changed = true;

                                // Count actual replacements
                                let matches = originalText.match(searchPattern);
                                if (matches) {
                                    replacements += matches.length;
                                }
                            }
                        }
                    }

                    if (changed) {
                        ann.richContents = ann.richContents; // Trigger update
                    }
                } else if (ann.contents) {
                    // Handle plain text contents
                    let originalText = ann.contents;
                    let newText = originalText.replace(searchPattern, replaceText);

                    if (newText !== originalText) {
                        ann.contents = newText;

                        // Count actual replacements
                        let matches = originalText.match(searchPattern);
                        if (matches) {
                            replacements += matches.length;
                        }
                    }
                }
            }

        } catch (error) {
            console.println("Error processing annotations: " + error.message);
        }

        return replacements;
    },

    // Process document text for find/replace with multiple extraction methods
    processDocumentText: function(target, findPattern, replaceText, config) {
        let stats = {
            replacements: 0,
            pagesProcessed: 0,
            extractionMethod: "none",
            textSample: "",
            unicodeIssues: false,
            extractionAttempts: [],
            totalWords: 0,
            emptyPages: 0
        };

        try {
            let doc = target;
            if (!doc || typeof doc.numPages === 'undefined') {
                console.println("Warning: Document text processing not available - document object not accessible");
                return stats;
            }

            console.println("Processing document with " + doc.numPages + " pages");

            // Build search pattern with enhanced Unicode support
            let searchPattern;
            if (config.useRegex) {
                let flags = this.buildRegexFlags(config);
                // Add Unicode flag if supported
                if (flags.indexOf('u') === -1) {
                    try {
                        // Test if Unicode flag is supported
                        new RegExp(findPattern, flags + 'u');
                        flags += 'u';
                    } catch (e) {
                        // Unicode flag not supported, continue without it
                    }
                }
                searchPattern = new RegExp(findPattern, flags);
            } else {
                let flags = this.buildRegexFlags(config);
                searchPattern = new RegExp(this.escapeRegex(findPattern), flags);
            }

            console.println("Search pattern: " + searchPattern.toString());

            // Process each page with multiple extraction methods
            for (let pageNum = 0; pageNum < doc.numPages; pageNum++) {
                try {
                    let pageText = "";
                    let extractionSuccess = false;
                    let pageStats = { words: 0, nonEmptyWords: 0, chineseChars: 0 };

                    console.println("\n=== Processing Page " + (pageNum + 1) + " ===");

                    // Method 1: Enhanced word-by-word extraction with character analysis
                    if (typeof doc.getPageNthWord !== 'undefined' && typeof doc.getPageNumWords !== 'undefined') {
                        try {
                            let numWords = doc.getPageNumWords(pageNum);
                            pageStats.words = numWords;
                            stats.totalWords += numWords;

                            console.println("Page " + (pageNum + 1) + " reports " + numWords + " words");

                            if (numWords > 0) {
                                let words = [];
                                let wordDetails = [];

                                for (let wordIndex = 0; wordIndex < numWords; wordIndex++) {
                                    try {
                                        let word = doc.getPageNthWord(pageNum, wordIndex);

                                        if (word !== null && word !== undefined) {
                                            // Analyze word content
                                            let wordInfo = {
                                                index: wordIndex,
                                                text: word,
                                                length: word.length,
                                                isEmpty: word.trim().length === 0,
                                                isWhitespace: /^\s*$/.test(word),
                                                hasChinese: /[\u4e00-\u9fff]/.test(word),
                                                charCodes: word.split('').map(c => c.charCodeAt(0))
                                            };

                                            wordDetails.push(wordInfo);

                                            if (!wordInfo.isEmpty) {
                                                words.push(word);
                                                pageStats.nonEmptyWords++;

                                                if (wordInfo.hasChinese) {
                                                    pageStats.chineseChars += (word.match(/[\u4e00-\u9fff]/g) || []).length;
                                                }
                                            }
                                        }
                                    } catch (e) {
                                        console.println("Warning: Could not extract word " + wordIndex + " on page " + (pageNum + 1) + ": " + e.message);
                                    }
                                }

                                // Log detailed analysis for first few words
                                if (pageNum === 0 && wordDetails.length > 0) {
                                    console.println("First 10 words analysis:");
                                    for (let i = 0; i < Math.min(10, wordDetails.length); i++) {
                                        let w = wordDetails[i];
                                        console.println("  Word " + i + ": '" + w.text + "' (len:" + w.length +
                                                      ", empty:" + w.isEmpty + ", chinese:" + w.hasChinese +
                                                      ", codes:" + w.charCodes.slice(0, 5).join(',') + ")");
                                    }
                                }

                                console.println("Page " + (pageNum + 1) + " stats: " + pageStats.words + " total words, " +
                                              pageStats.nonEmptyWords + " non-empty, " + pageStats.chineseChars + " Chinese chars");

                                if (words.length > 0) {
                                    pageText = words.join(" ");
                                    extractionSuccess = true;
                                    stats.extractionMethod = "enhanced-word-by-word";

                                    // Store sample for debugging
                                    if (pageNum === 0 && !stats.textSample) {
                                        stats.textSample = pageText.substring(0, 200);
                                    }
                                } else {
                                    console.println("All words were empty/whitespace on page " + (pageNum + 1));
                                    stats.emptyPages++;
                                }
                            }
                        } catch (e) {
                            console.println("Enhanced word extraction failed on page " + (pageNum + 1) + ": " + e.message);
                            stats.extractionAttempts.push("word-by-word failed: " + e.message);
                        }
                    }

                    // Method 2: Try alternative text extraction approaches
                    if (!extractionSuccess) {
                        let alternativeMethods = [
                            { name: "getPageText", func: () => doc.getPageText(pageNum) },
                            { name: "extractText", func: () => doc.extractText(pageNum) },
                            { name: "getPageContent", func: () => doc.getPageContent(pageNum) },
                            { name: "getText", func: () => doc.getText(pageNum) }
                        ];

                        for (let method of alternativeMethods) {
                            if (!extractionSuccess) {
                                try {
                                    if (typeof method.func === 'function') {
                                        let result = method.func();
                                        if (result && result.length > 0 && result.trim().length > 0) {
                                            pageText = result;
                                            extractionSuccess = true;
                                            stats.extractionMethod = method.name;
                                            console.println("Success with method: " + method.name);

                                            if (pageNum === 0 && !stats.textSample) {
                                                stats.textSample = pageText.substring(0, 200);
                                            }
                                            break;
                                        }
                                    }
                                } catch (e) {
                                    stats.extractionAttempts.push(method.name + " failed: " + e.message);
                                }
                            }
                        }
                    }

                    // Method 3: Try to extract text from page annotations as fallback
                    if (!extractionSuccess) {
                        try {
                            let pageAnnots = [];
                            if (doc.getPageAnnots) {
                                pageAnnots = doc.getPageAnnots(pageNum);
                            } else if (doc.getAnnots) {
                                // Filter annotations for this page
                                let allAnnots = doc.getAnnots();
                                pageAnnots = allAnnots.filter(ann => ann.page === pageNum);
                            }

                            if (pageAnnots && pageAnnots.length > 0) {
                                let annotTexts = [];
                                for (let ann of pageAnnots) {
                                    if (ann.contents) {
                                        annotTexts.push(ann.contents);
                                    }
                                    if (ann.richContents && ann.richContents.length > 0) {
                                        for (let rich of ann.richContents) {
                                            if (rich.text) {
                                                annotTexts.push(rich.text);
                                            }
                                        }
                                    }
                                }

                                if (annotTexts.length > 0) {
                                    pageText = annotTexts.join(" ");
                                    extractionSuccess = true;
                                    stats.extractionMethod = "page-annotations";
                                    console.println("Extracted text from " + annotTexts.length + " annotations on page " + (pageNum + 1));
                                }
                            }
                        } catch (e) {
                            stats.extractionAttempts.push("page-annotations failed: " + e.message);
                        }
                    }

                    // Method 4: Critical workaround for whitespace-only extraction
                    if (!extractionSuccess || (pageText && pageText.trim().length === 0)) {
                        console.println("⚠️  CRITICAL: Text extraction returned only whitespace/empty content");
                        console.println("This indicates a PDF-XChange Editor API limitation with this document type");

                        // Try to detect if this is a searchable PDF by attempting selection
                        try {
                            // Alternative approach: Try to access document properties that might indicate text content
                            if (doc.info) {
                                console.println("Document info available - may contain searchable text");
                            }

                            // Check if document has text layers (indicates searchable PDF)
                            if (typeof doc.getPageBox !== 'undefined') {
                                try {
                                    let mediaBox = doc.getPageBox("Media", pageNum);
                                    console.println("Page " + (pageNum + 1) + " MediaBox: " + JSON.stringify(mediaBox));
                                } catch (e) {
                                    // Not critical
                                }
                            }

                            // Last resort: Inform user about the limitation
                            if (pageNum === 0) {
                                stats.extractionMethod = "api-limitation";
                                stats.textSample = "⚠️  PDF-XChange Editor JavaScript API cannot extract text from this document type. " +
                                                 "This may be due to: 1) Document security settings, 2) PDF structure incompatibility, " +
                                                 "3) Text stored as images/vectors rather than searchable text, or 4) API version limitations.";
                            }

                        } catch (e) {
                            console.println("Document analysis failed: " + e.message);
                        }
                    }

                    if (extractionSuccess && pageText && pageText.trim().length > 0) {
                        // Check for Unicode issues
                        let hasUnicode = /[\u0080-\uFFFF]/.test(pageText);
                        let hasChinese = /[\u4e00-\u9fff]/.test(pageText);

                        if (hasUnicode) {
                            stats.unicodeIssues = !hasChinese && findPattern.match(/[\u4e00-\u9fff]/);
                        }

                        console.println("Page " + (pageNum + 1) + " text length: " + pageText.length +
                                      ", Unicode: " + hasUnicode + ", Chinese: " + hasChinese);

                        // Show sample of extracted text for debugging
                        if (pageText.length > 0) {
                            let sample = pageText.substring(0, 100);
                            console.println("Text sample: '" + sample + "'");
                            console.println("Character codes: " + sample.split('').slice(0, 20).map(c => c.charCodeAt(0)).join(','));
                        }

                        // Perform pattern matching
                        let originalText = pageText;
                        let matches = originalText.match(searchPattern);

                        if (matches && matches.length > 0) {
                            console.println("✅ Found " + matches.length + " matches on page " + (pageNum + 1));
                            console.println("Sample matches: " + matches.slice(0, 3).join(", "));
                            stats.replacements += matches.length;

                            // Note: Actual text replacement in PDF documents requires
                            // advanced API methods that may not be available in all versions
                            // For now, we only count matches
                        } else {
                            console.println("❌ No matches found on page " + (pageNum + 1));
                        }

                        stats.pagesProcessed++;
                    } else {
                        console.println("❌ No usable text extracted from page " + (pageNum + 1));
                        if (pageText) {
                            console.println("Extracted text was: '" + pageText.substring(0, 50) + "' (length: " + pageText.length + ")");
                        }
                    }

                } catch (pageError) {
                    console.println("Error processing page " + (pageNum + 1) + ": " + pageError.message);
                }
            }

            // Final summary
            console.println("\n=== Document Processing Summary ===");
            console.println("Extraction Method: " + stats.extractionMethod);
            console.println("Pages Processed: " + stats.pagesProcessed + "/" + doc.numPages);
            console.println("Total Words Reported: " + stats.totalWords);
            console.println("Empty Pages: " + stats.emptyPages);
            console.println("Matches Found: " + stats.replacements);
            console.println("Extraction Attempts: " + stats.extractionAttempts.length);

            if (stats.extractionAttempts.length > 0) {
                console.println("Failed Methods:");
                for (let attempt of stats.extractionAttempts) {
                    console.println("  - " + attempt);
                }
            }

            if (stats.extractionMethod === "api-limitation") {
                console.println("\n🚨 CRITICAL ISSUE DETECTED:");
                console.println("PDF-XChange Editor's JavaScript API cannot extract text from this document.");
                console.println("Possible causes:");
                console.println("1. Document has security restrictions preventing text access");
                console.println("2. Text is stored as images/graphics rather than searchable text");
                console.println("3. PDF structure is incompatible with the JavaScript API");
                console.println("4. Document uses unsupported text encoding");
                console.println("\n💡 Recommended solutions:");
                console.println("1. Try searching in 'annotations' scope only");
                console.println("2. Check if text can be copied manually from the PDF");
                console.println("3. Use PDF-XChange Editor's built-in search (Ctrl+F) to verify text is searchable");
                console.println("4. Consider using OCR if this is a scanned document");
            }

        } catch (error) {
            console.println("Error processing document text: " + error.message);
            stats.extractionAttempts.push("Fatal error: " + error.message);
        }

        return stats;
    },

    // Format results for display
    formatResults: function(stats, config) {
        let result = "Find/Replace Operation Complete:\n\n";

        result += "Search Pattern: " + config.findText + "\n";
        result += "Replace Text: " + config.replaceText + "\n";
        result += "Mode: " + (config.useRegex ? "Regular Expression" : "Literal Text") + "\n";
        result += "Case Sensitive: " + (config.caseSensitive ? "Yes" : "No") + "\n";
        result += "Search Scope: " + config.searchScope + "\n\n";

        result += "Results:\n";
        result += "Total Replacements: " + stats.totalMatches + "\n";

        if (config.searchScope === "annotations" || config.searchScope === "both") {
            result += "Annotation Replacements: " + stats.annotationReplacements + "\n";
        }

        if (config.searchScope === "document" || config.searchScope === "both") {
            result += "Document Replacements: " + stats.documentReplacements + "\n";
            result += "Pages Processed: " + stats.pagesProcessed + "\n";

            // Add diagnostic information for document processing
            if (stats.extractionMethod) {
                result += "Text Extraction Method: " + stats.extractionMethod + "\n";
            }

            if (stats.textSample) {
                result += "Text Sample: " + stats.textSample.substring(0, 100) + "...\n";
            }

            if (stats.unicodeIssues) {
                result += "⚠️  Unicode Issues Detected: Text may not contain expected characters\n";
            }
        }

        if (stats.totalMatches === 0) {
            result += "\n❌ No matches found for the specified pattern.\n";
            result += "\n🔍 Troubleshooting Tips:\n";
            result += "1. Verify the pattern matches your actual text\n";
            result += "2. Check if text is in annotations vs document content\n";
            result += "3. Try a simpler pattern first (e.g., just the Chinese characters)\n";
            result += "4. Use the diagnostic helper: regexDiagnostics.runFullDiagnostics(this)\n";

            if (config.useRegex && config.findText.match(/[\u4e00-\u9fff]/)) {
                result += "5. Chinese text may require special handling - check console for details\n";
            }
        }

        return result;
    },

    // Save search history as arrays
    mkArrays: function(globalData) {
        let fieldsToSave = ["findText", "replaceText"];
        let newData = this.dialog.data;

        if (globalData) {
            for (let field of fieldsToSave) {
                let arr = globalData[field];
                if (arr) {
                    if (!Array.isArray(arr)) {
                        arr = [arr];
                    }

                    // Add current value to history if not already present
                    if (newData[field] && arr.indexOf(newData[field]) === -1) {
                        newData[field] = [newData[field]].concat(arr);
                    } else {
                        newData[field] = arr;
                    }

                    // Limit history length
                    if (newData[field].length > this.SAVE_LENGTH) {
                        newData[field] = newData[field].slice(0, this.SAVE_LENGTH);
                    }
                }
            }
        }

        return newData;
    }
};

// Global storage functionality with trusted functions
regexFindReplace.global = new class {
    constructor(name) {
        this.get = app.trustedFunction(() => {
            app.beginPriv();
            try {
                if (global[name]) {
                    return JSON.parse(global[name]);
                }
                return {};
            } catch (error) {
                console.println("Error accessing global variable '" + name + "': " + error.message);
                console.println("Try unchecking 'Enable global object security policy' in preferences,");
                console.println("or edit the 'GlobData' file to remove conflicting entries.");
                return {};
            }
        });

        this.set = app.trustedFunction(value => {
            app.beginPriv();
            try {
                global[name] = JSON.stringify(value);
                global.setPersistent(name, true);
            } catch (error) {
                console.println("Error saving global variable '" + name + "': " + error.message);
            }
        });
    }
}("RegexFindReplaceSettings");

// Utility functions for common regex patterns
regexFindReplace.patterns = {
    // Common regex patterns for quick access
    email: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    phone: /\b\d{3}[-.]?\d{3}[-.]?\d{4}\b/g,
    url: /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/g,
    date: /\b\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}\b/g,
    time: /\b\d{1,2}:\d{2}(?::\d{2})?\s?(?:AM|PM|am|pm)?\b/g,

    // Helper function to get pattern description
    getDescription: function(patternName) {
        const descriptions = {
            email: "Email addresses (<EMAIL>)",
            phone: "Phone numbers (************, ************, 1234567890)",
            url: "URLs (http://example.com, https://www.example.com)",
            date: "Dates (MM/DD/YYYY, MM-DD-YYYY)",
            time: "Time (HH:MM, HH:MM:SS, with AM/PM)"
        };
        return descriptions[patternName] || "Unknown pattern";
    }
};

// Console logging for debugging
console.println("Enhanced Regex Find/Replace Plugin v2.0.0 loaded successfully");
console.println("Available features:");
console.println("- Regular expression find and replace");
console.println("- Batch processing support");
console.println("- Document and annotation text processing");
console.println("- Advanced regex validation");
console.println("- Search history and settings persistence");
