# Chinese Text Troubleshooting Guide

## 🚨 **Your Specific Issue**

**Problem**: Plugin reports "0 matches" for Chinese bank transaction pattern despite visible matching text.

**Pattern**: `中国工商银行\s+\d{8}\s+\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\s+[a-zA-Z0-9]{12}`

**Expected Match**: `中国工商银行 ******** 23-11-15 14:30:25 ABC123DEF456`

## 🔍 **Immediate Diagnostic Steps**

### Step 1: Load Diagnostic Helper
1. Copy `diagnostic-helper.js` to your JavaScript folder
2. Restart PDF-XChange Editor
3. Open your PDF with Chinese text
4. Open JavaScript Console: `View > Panes > JavaScript Console`
5. Run: `regexDiagnostics.runFullDiagnostics(this)`

### Step 2: Check Text Extraction
The diagnostic will show:
- Whether Chinese characters are being extracted correctly
- Which text extraction method works
- Unicode handling capabilities
- Sample text from your PDF

### Step 3: Verify Pattern Matching
Test your pattern with extracted text:
```javascript
// In JavaScript Console:
let pattern = /中国工商银行\s+\d{8}\s+\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\s+[a-zA-Z0-9]{12}/g;
let testText = "中国工商银行 ******** 23-11-15 14:30:25 ABC123DEF456";
console.println("Test result: " + testText.match(pattern));
```

## 🛠️ **Common Solutions**

### Solution 1: PDF Type Issues

**If your PDF is image-based (scanned)**:
- ❌ **Problem**: Text is actually an image, not searchable text
- ✅ **Solution**: Use OCR software to convert to searchable PDF first
- 🔍 **Test**: Try to select and copy text from the PDF. If you can't, it's image-based.

### Solution 2: Text Encoding Issues

**If Chinese characters appear as boxes or question marks**:
```javascript
// Test in console:
regexDiagnostics.testUnicodeHandling();
```

**Solutions**:
- Update PDF-XChange Editor to latest version
- Check if PDF was created with proper Unicode encoding
- Try different text extraction methods

### Solution 3: Text Extraction Method

**If diagnostic shows no text extraction**:
1. Try different search scopes:
   - Set scope to "annotations" if text is in comments/markup
   - Set scope to "document" for main document text
   - Set scope to "both" to cover all possibilities

### Solution 4: Pattern Refinement

**Start with simpler patterns**:

1. **Test Chinese characters only**:
   ```
   Pattern: 中国工商银行
   ```

2. **Test numbers only**:
   ```
   Pattern: \d{8}
   ```

3. **Test date format**:
   ```
   Pattern: \d{2}-\d{2}-\d{2}
   ```

4. **Gradually combine**:
   ```
   Pattern: 中国工商银行.*\d{8}
   ```

### Solution 5: Whitespace Issues

**Chinese text may have different whitespace characters**:

**Try flexible whitespace patterns**:
```
Original: 中国工商银行\s+\d{8}
Try: 中国工商银行[\s\u00A0\u3000]+\d{8}
```

**Explanation**:
- `\u00A0` = Non-breaking space
- `\u3000` = Ideographic space (common in Chinese text)

## 🔧 **Advanced Troubleshooting**

### Check PDF Structure
```javascript
// In console, check document properties:
console.println("Document title: " + this.title);
console.println("Document author: " + this.author);
console.println("Document creator: " + this.creator);
console.println("Number of pages: " + this.numPages);

// Check if document has form fields:
try {
    let fields = this.getFields();
    console.println("Form fields: " + fields.length);
} catch(e) {
    console.println("No form fields or error: " + e.message);
}
```

### Test Different Regex Flags
```javascript
// Test with Unicode flag:
let pattern1 = new RegExp("中国工商银行", "gu");
let pattern2 = new RegExp("中国工商银行", "gi");

// Test both patterns with your text
```

### Manual Text Verification
1. **Copy text from PDF**: Select and copy a line that should match
2. **Paste into console**:
   ```javascript
   let copiedText = "paste your copied text here";
   console.println("Text length: " + copiedText.length);
   console.println("Character codes: " + copiedText.split('').map(c => c.charCodeAt(0)).join(', '));
   
   // Test pattern matching:
   let pattern = /中国工商银行\s+\d{8}\s+\d{2}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2}\s+[a-zA-Z0-9]{12}/g;
   console.println("Matches: " + copiedText.match(pattern));
   ```

## 🎯 **Specific Solutions for Your Pattern**

### Enhanced Pattern for Chinese Banking Data
```javascript
// More flexible pattern:
let flexiblePattern = /中国工商银行[\s\u00A0\u3000]+\d{8}[\s\u00A0\u3000]+\d{2}[-\u2010\u2011\u2012\u2013\u2014\u2015]\d{2}[-\u2010\u2011\u2012\u2013\u2014\u2015]\d{2}[\s\u00A0\u3000]+\d{2}:\d{2}:\d{2}[\s\u00A0\u3000]+[a-zA-Z0-9]{12}/g;
```

**Improvements**:
- Multiple whitespace types: `[\s\u00A0\u3000]+`
- Multiple dash types: `[-\u2010\u2011\u2012\u2013\u2014\u2015]`
- More flexible spacing

### Step-by-Step Pattern Building
```javascript
// Test each component:
let tests = [
    { pattern: "中国工商银行", desc: "Bank name" },
    { pattern: "\\d{8}", desc: "8 digits" },
    { pattern: "\\d{2}-\\d{2}-\\d{2}", desc: "Date format" },
    { pattern: "\\d{2}:\\d{2}:\\d{2}", desc: "Time format" },
    { pattern: "[a-zA-Z0-9]{12}", desc: "12 alphanumeric" }
];

// Test each with your actual text
```

## 📋 **Diagnostic Checklist**

Run through this checklist and report results:

- [ ] **PDF Type**: Can you select and copy text? (Yes/No)
- [ ] **Chinese Display**: Do Chinese characters display correctly when copied? (Yes/No)
- [ ] **Text Extraction**: Does diagnostic show extracted Chinese text? (Yes/No)
- [ ] **Pattern Test**: Does simple pattern `中国工商银行` match? (Yes/No)
- [ ] **Console Errors**: Any JavaScript errors in console? (List them)
- [ ] **Plugin Loading**: Does plugin menu item appear? (Yes/No)
- [ ] **Sample Text**: Paste actual copied text from PDF

## 🚀 **Quick Fix Attempts**

### Try These Patterns in Order:

1. **Simplest**: `中国工商银行`
2. **With numbers**: `中国工商银行.*\d{8}`
3. **With flexible space**: `中国工商银行[\s\u3000]+\d{8}`
4. **Full flexible**: `中国工商银行[\s\u00A0\u3000]+\d{8}[\s\u00A0\u3000]+\d{2}[-\u2010-\u2015]\d{2}[-\u2010-\u2015]\d{2}[\s\u00A0\u3000]+\d{2}:\d{2}:\d{2}[\s\u00A0\u3000]+[a-zA-Z0-9]{12}`

### Settings to Try:
- ✅ Use Regular Expressions: **checked**
- ❌ Case Sensitive: **unchecked**
- ✅ Multiline Mode: **checked** (try both ways)
- ❌ Dot Matches All: **unchecked**
- 🔄 Search Scope: Try **"both"**, then **"document"**, then **"annotations"**

## 📞 **Next Steps**

1. **Run diagnostics** and share the console output
2. **Copy and paste** actual text from your PDF
3. **Test simple patterns** first before complex ones
4. **Try different search scopes** systematically
5. **Check PDF properties** to understand document structure

**If still not working**, provide:
- Diagnostic output from console
- Actual copied text from PDF
- PDF creation method (scanned, generated, etc.)
- PDF-XChange Editor exact version number

This will help identify the specific issue and provide a targeted solution.
