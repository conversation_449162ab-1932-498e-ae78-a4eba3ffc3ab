/*
 * Ghost Text Extractor for PDF-XChange Editor
 * Specialized tool for extracting "invisible" Chinese text from OpenPDF-generated documents
 * Addresses the specific issue where getPageNthWord returns empty strings despite word counts
 */

var ghostTextExtractor = {
    
    // Advanced text extraction using multiple encoding attempts
    extractGhostText: function(target, pageNum) {
        console.println("=== Ghost Text Extraction for Page " + (pageNum + 1) + " ===");
        
        let results = {
            totalWords: 0,
            extractedWords: [],
            chineseWords: [],
            encodingAttempts: [],
            success: false
        };
        
        try {
            if (typeof target.getPageNumWords === 'undefined' || typeof target.getPageNthWord === 'undefined') {
                console.println("❌ Required API methods not available");
                return results;
            }
            
            let numWords = target.getPageNumWords(pageNum);
            results.totalWords = numWords;
            console.println("Reported words on page " + (pageNum + 1) + ": " + numWords);
            
            if (numWords === 0) {
                console.println("No words reported on this page");
                return results;
            }
            
            // Method 1: Try different character encoding interpretations
            for (let wordIndex = 0; wordIndex < Math.min(numWords, 200); wordIndex++) {
                try {
                    let rawWord = target.getPageNthWord(pageNum, wordIndex);
                    
                    if (rawWord === null || rawWord === undefined) {
                        continue;
                    }
                    
                    // Attempt 1: Direct extraction
                    if (rawWord && rawWord.length > 0 && rawWord.trim().length > 0) {
                        results.extractedWords.push({
                            index: wordIndex,
                            text: rawWord,
                            method: "direct",
                            hasChinese: /[\u4e00-\u9fff]/.test(rawWord)
                        });
                        
                        if (/[\u4e00-\u9fff]/.test(rawWord)) {
                            results.chineseWords.push(rawWord);
                        }
                        continue;
                    }
                    
                    // Attempt 2: Try to decode as different encodings
                    let encodingAttempts = [
                        { name: "UTF-8", process: (w) => this.tryUTF8Decode(w) },
                        { name: "GB2312", process: (w) => this.tryGB2312Decode(w) },
                        { name: "Unicode", process: (w) => this.tryUnicodeDecode(w) },
                        { name: "Hex", process: (w) => this.tryHexDecode(w) }
                    ];
                    
                    for (let attempt of encodingAttempts) {
                        try {
                            let decoded = attempt.process(rawWord);
                            if (decoded && decoded.length > 0 && decoded.trim().length > 0) {
                                results.extractedWords.push({
                                    index: wordIndex,
                                    text: decoded,
                                    method: attempt.name,
                                    hasChinese: /[\u4e00-\u9fff]/.test(decoded)
                                });
                                
                                if (/[\u4e00-\u9fff]/.test(decoded)) {
                                    results.chineseWords.push(decoded);
                                }
                                break;
                            }
                        } catch (e) {
                            // Encoding attempt failed, try next
                        }
                    }
                    
                } catch (e) {
                    console.println("Error processing word " + wordIndex + ": " + e.message);
                }
            }
            
            results.success = results.extractedWords.length > 0;
            
            console.println("Extraction Results:");
            console.println("  Total words processed: " + Math.min(numWords, 200));
            console.println("  Successfully extracted: " + results.extractedWords.length);
            console.println("  Chinese words found: " + results.chineseWords.length);
            
            if (results.chineseWords.length > 0) {
                console.println("  Sample Chinese words: " + results.chineseWords.slice(0, 10).join(", "));
            }
            
        } catch (e) {
            console.println("Ghost text extraction error: " + e.message);
        }
        
        return results;
    },
    
    // Try UTF-8 decoding
    tryUTF8Decode: function(word) {
        if (!word) return null;
        
        try {
            // If word contains escape sequences or encoded characters
            if (word.indexOf('\\u') !== -1) {
                return word.replace(/\\u[\dA-F]{4}/gi, function(match) {
                    return String.fromCharCode(parseInt(match.replace(/\\u/g, ''), 16));
                });
            }
            
            // Try decodeURIComponent if it looks URL-encoded
            if (word.indexOf('%') !== -1) {
                return decodeURIComponent(word);
            }
            
            return null;
        } catch (e) {
            return null;
        }
    },
    
    // Try GB2312/GBK decoding (simplified approach)
    tryGB2312Decode: function(word) {
        if (!word) return null;
        
        try {
            // Look for high-bit characters that might be GB2312
            let hasHighBit = false;
            for (let i = 0; i < word.length; i++) {
                if (word.charCodeAt(i) > 127) {
                    hasHighBit = true;
                    break;
                }
            }
            
            if (hasHighBit) {
                // This is a simplified approach - full GB2312 decoding would require lookup tables
                // For now, just return the word as-is if it has high-bit characters
                return word;
            }
            
            return null;
        } catch (e) {
            return null;
        }
    },
    
    // Try Unicode decoding
    tryUnicodeDecode: function(word) {
        if (!word) return null;
        
        try {
            // Look for Unicode escape sequences
            if (word.match(/&#\d+;/)) {
                return word.replace(/&#(\d+);/g, function(match, dec) {
                    return String.fromCharCode(dec);
                });
            }
            
            if (word.match(/&#x[0-9A-Fa-f]+;/)) {
                return word.replace(/&#x([0-9A-Fa-f]+);/g, function(match, hex) {
                    return String.fromCharCode(parseInt(hex, 16));
                });
            }
            
            return null;
        } catch (e) {
            return null;
        }
    },
    
    // Try hex decoding
    tryHexDecode: function(word) {
        if (!word) return null;
        
        try {
            // If word looks like hex-encoded data
            if (word.match(/^[0-9A-Fa-f]+$/) && word.length % 2 === 0 && word.length >= 4) {
                let result = "";
                for (let i = 0; i < word.length; i += 2) {
                    let hex = word.substr(i, 2);
                    let charCode = parseInt(hex, 16);
                    if (charCode > 0) {
                        result += String.fromCharCode(charCode);
                    }
                }
                
                if (result.length > 0 && /[\u4e00-\u9fff]/.test(result)) {
                    return result;
                }
            }
            
            return null;
        } catch (e) {
            return null;
        }
    },
    
    // Alternative approach: Try to extract text using PDF-XChange Editor's search functionality
    trySearchBasedExtraction: function(target, searchTerm) {
        console.println("=== Search-Based Text Extraction ===");
        console.println("Attempting to extract text using search functionality...");
        
        try {
            // This is a conceptual approach - PDF-XChange Editor's JavaScript API
            // may not expose search results, but we can try
            
            if (typeof target.search !== 'undefined') {
                let searchResults = target.search(searchTerm);
                if (searchResults && searchResults.length > 0) {
                    console.println("Search found " + searchResults.length + " results");
                    return searchResults;
                }
            }
            
            // Alternative: Try to use the built-in search and capture results
            console.println("Direct search API not available");
            console.println("💡 Manual verification needed:");
            console.println("1. Press Ctrl+F in PDF-XChange Editor");
            console.println("2. Search for: 中国工商银行");
            console.println("3. If found, the text IS searchable but API cannot extract it");
            
            return null;
            
        } catch (e) {
            console.println("Search-based extraction error: " + e.message);
            return null;
        }
    },
    
    // Comprehensive ghost text analysis
    analyzeGhostText: function(target) {
        console.println("🔍 Starting Comprehensive Ghost Text Analysis");
        console.println("Document: " + (target.title || "Unknown"));
        console.println("==========================================");
        
        let overallResults = {
            totalPages: target.numPages,
            pagesAnalyzed: 0,
            totalWords: 0,
            extractedWords: 0,
            chineseWords: 0,
            extractionMethods: {},
            recommendations: []
        };
        
        // Analyze first 3 pages in detail
        let pagesToAnalyze = Math.min(3, target.numPages);
        
        for (let pageNum = 0; pageNum < pagesToAnalyze; pageNum++) {
            console.println("\n--- Analyzing Page " + (pageNum + 1) + " ---");
            
            let pageResults = this.extractGhostText(target, pageNum);
            
            overallResults.pagesAnalyzed++;
            overallResults.totalWords += pageResults.totalWords;
            overallResults.extractedWords += pageResults.extractedWords.length;
            overallResults.chineseWords += pageResults.chineseWords.length;
            
            // Track successful extraction methods
            for (let word of pageResults.extractedWords) {
                if (!overallResults.extractionMethods[word.method]) {
                    overallResults.extractionMethods[word.method] = 0;
                }
                overallResults.extractionMethods[word.method]++;
            }
        }
        
        // Generate recommendations
        console.println("\n=== ANALYSIS SUMMARY ===");
        console.println("Pages analyzed: " + overallResults.pagesAnalyzed);
        console.println("Total words reported: " + overallResults.totalWords);
        console.println("Words successfully extracted: " + overallResults.extractedWords);
        console.println("Chinese words found: " + overallResults.chineseWords);
        
        if (overallResults.extractedWords === 0) {
            console.println("\n🚨 GHOST TEXT CONFIRMED");
            console.println("This PDF has the classic 'ghost text' problem:");
            console.println("- PDF reports word counts but text is not extractable");
            console.println("- Likely caused by OpenPDF's text rendering method");
            console.println("- Chinese characters stored as graphics with invisible text layer");
            
            overallResults.recommendations = [
                "Use PDF-XChange Editor's built-in search (Ctrl+F) to verify text is searchable",
                "If Ctrl+F finds text, consider using annotation-based search instead",
                "Try OCR processing to create a new searchable text layer",
                "Contact document creator to regenerate PDF with proper text encoding",
                "Use alternative PDF processing tools that can handle this specific issue"
            ];
        } else if (overallResults.chineseWords > 0) {
            console.println("\n✅ SUCCESS - Chinese text extracted!");
            console.println("Successful extraction methods:");
            for (let method in overallResults.extractionMethods) {
                console.println("  " + method + ": " + overallResults.extractionMethods[method] + " words");
            }
        }
        
        console.println("\n💡 RECOMMENDATIONS:");
        for (let i = 0; i < overallResults.recommendations.length; i++) {
            console.println((i + 1) + ". " + overallResults.recommendations[i]);
        }
        
        return overallResults;
    }
};

// Auto-load message
console.println("👻 Ghost Text Extractor loaded");
console.println("📋 Usage: ghostTextExtractor.analyzeGhostText(this)");
console.println("🎯 Specialized for OpenPDF-generated Chinese documents with extraction issues");
