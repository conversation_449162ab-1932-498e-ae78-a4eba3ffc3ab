# Regex Find/Replace Plugin - Usage Examples

## Table of Contents
1. [Basic Text Replacement](#basic-text-replacement)
2. [Regular Expression Patterns](#regular-expression-patterns)
3. [Data Formatting](#data-formatting)
4. [Batch Processing Scenarios](#batch-processing-scenarios)
5. [Document Cleanup](#document-cleanup)
6. [Advanced Regex Techniques](#advanced-regex-techniques)

## Basic Text Replacement

### Simple Word Replacement
**Scenario**: Replace all instances of "Company ABC" with "XYZ Corporation"

**Settings**:
- Find Pattern: `Company ABC`
- Replace With: `XYZ Corporation`
- Use Regular Expressions: ❌ (unchecked)
- Case Sensitive: ❌ (unchecked)

### Case-Sensitive Replacement
**Scenario**: Replace "PDF" with "Portable Document Format" but keep "pdf" unchanged

**Settings**:
- Find Pattern: `PDF`
- Replace With: `Portable Document Format`
- Use Regular Expressions: ❌ (unchecked)
- Case Sensitive: ✅ (checked)

## Regular Expression Patterns

### Email Address Processing

#### Find All Email Addresses
**Pattern**: `\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b`

**Example Matches**:
- <EMAIL>
- <EMAIL>
- <EMAIL>

#### Convert Email to Mailto Links
**Find**: `\b([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,})\b`
**Replace**: `mailto:$1`

**Result**: `<EMAIL>` → `mailto:<EMAIL>`

### Phone Number Formatting

#### Standardize Phone Numbers
**Find**: `\b(\d{3})[-.\s]?(\d{3})[-.\s]?(\d{4})\b`
**Replace**: `($1) $2-$3`

**Examples**:
- `************` → `(*************`
- `************` → `(*************`
- `1234567890` → `(*************`

#### Extract Area Codes
**Find**: `\b(\d{3})[-.\s]?\d{3}[-.\s]?\d{4}\b`
**Replace**: `Area Code: $1`

### URL Processing

#### Find All URLs
**Pattern**: `https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)`

#### Convert HTTP to HTTPS
**Find**: `http:\/\/([^\/\s]+)`
**Replace**: `https://$1`

## Data Formatting

### Date Format Conversion

#### MM/DD/YYYY to DD-MM-YYYY
**Find**: `\b(\d{1,2})\/(\d{1,2})\/(\d{4})\b`
**Replace**: `$2-$1-$3`

**Example**: `12/25/2023` → `25-12-2023`

#### Add Ordinal Suffixes to Dates
**Find**: `\b(\d{1,2})(st|nd|rd|th)?\s+(January|February|March|April|May|June|July|August|September|October|November|December)\s+(\d{4})\b`
**Replace**: `$1st $3 $4`

### Currency Formatting

#### Add Currency Symbols
**Find**: `\b(\d+\.?\d*)\s*(dollars?|USD)\b`
**Replace**: `$$$1`

**Example**: `100 dollars` → `$100`

#### Format Large Numbers
**Find**: `\b(\d{1,3})(\d{3})\b`
**Replace**: `$1,$2`

**Example**: `1000` → `1,000`

## Batch Processing Scenarios

### Document Standardization
**Scenario**: Standardize a document with multiple formatting issues

```json
[
  {
    "find": "\\b(\\d{1,2})\\/(\\d{1,2})\\/(\\d{4})\\b",
    "replace": "$2-$1-$3",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "\\b([A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,})\\b",
    "replace": "mailto:$1",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "\\b(\\d{3})[-.]?(\\d{3})[-.]?(\\d{4})\\b",
    "replace": "($1) $2-$3",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "Company ABC",
    "replace": "XYZ Corporation",
    "useRegex": false,
    "caseSensitive": false
  }
]
```

### Legal Document Processing
**Scenario**: Update legal references and formatting

```json
[
  {
    "find": "Section (\\d+)\\.(\\d+)",
    "replace": "§$1.$2",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "\\b(January|February|March|April|May|June|July|August|September|October|November|December)\\s+(\\d{1,2}),?\\s+(\\d{4})\\b",
    "replace": "$2 $1 $3",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "plaintiff",
    "replace": "Plaintiff",
    "useRegex": false,
    "caseSensitive": true
  }
]
```

## Document Cleanup

### Remove Extra Whitespace

#### Multiple Spaces to Single Space
**Find**: `\s{2,}`
**Replace**: ` `

#### Remove Trailing Spaces
**Find**: `\s+$`
**Replace**: `` (empty)

#### Clean Up Line Breaks
**Find**: `\n{3,}`
**Replace**: `\n\n`

### Text Normalization

#### Fix Common Typos
```json
[
  {
    "find": "\\bteh\\b",
    "replace": "the",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "\\badn\\b",
    "replace": "and",
    "useRegex": true,
    "caseSensitive": false
  },
  {
    "find": "\\brecieve\\b",
    "replace": "receive",
    "useRegex": true,
    "caseSensitive": false
  }
]
```

#### Standardize Quotation Marks
**Find**: `[""]([^"""]*?)[""]`
**Replace**: `"$1"`

## Advanced Regex Techniques

### Lookahead and Lookbehind (Conceptual)
*Note: PDF-XChange Editor's JavaScript engine may not support all advanced regex features*

#### Find Numbers Not Followed by Percent
**Pattern**: `\d+(?!%)`

#### Find Words Not Preceded by "The"
**Pattern**: `(?<!The\s)\b[A-Z][a-z]+\b`

### Conditional Replacements

#### Capitalize First Letter of Sentences
**Find**: `(^|[.!?]\s+)([a-z])`
**Replace**: `$1$2` (with manual capitalization)

#### Format Scientific Notation
**Find**: `(\d+\.?\d*)[eE]([-+]?\d+)`
**Replace**: `$1 × 10^$2`

### Complex Pattern Matching

#### Extract Data from Structured Text
**Scenario**: Extract names from "Last, First" format

**Find**: `([A-Z][a-z]+),\s+([A-Z][a-z]+)`
**Replace**: `$2 $1`

**Example**: `Smith, John` → `John Smith`

#### Process Tabular Data
**Find**: `(\w+)\t(\w+)\t(\d+)`
**Replace**: `Name: $1, Category: $2, Value: $3`

## Tips for Effective Usage

### Testing Patterns
1. Start with simple patterns and gradually add complexity
2. Test on a small section before applying to entire document
3. Use online regex testers to validate patterns
4. Keep backups of important documents

### Performance Optimization
1. Use specific patterns rather than overly broad ones
2. Limit batch operations to essential changes
3. Process large documents in sections if needed
4. Monitor memory usage during complex operations

### Common Pitfalls
1. **Escaping in JSON**: Remember to double-escape backslashes in batch mode
2. **Greedy vs. Lazy**: Use `.*?` instead of `.*` for non-greedy matching
3. **Word Boundaries**: Use `\b` to avoid partial word matches
4. **Case Sensitivity**: Always consider whether case matters for your use case

### Best Practices
1. Document your regex patterns for future reference
2. Use descriptive replacement text when possible
3. Validate results after each operation
4. Keep a log of successful patterns for reuse
