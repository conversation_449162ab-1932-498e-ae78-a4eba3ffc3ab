/*
 * Test Scenarios for Enhanced Regex Find/Replace Plugin
 * 
 * This file contains test functions to verify plugin functionality.
 * Load this file alongside the main plugin for testing purposes.
 */

// Test utility object
var regexTestSuite = {
    
    // Test basic regex validation
    testRegexValidation: function() {
        console.println("=== Testing Regex Validation ===");
        
        let testCases = [
            { pattern: "test", flags: "gi", shouldPass: true },
            { pattern: "\\d+", flags: "g", shouldPass: true },
            { pattern: "[a-z", flags: "g", shouldPass: false }, // Invalid - unclosed bracket
            { pattern: "(?:test)", flags: "gi", shouldPass: true },
            { pattern: "*invalid", flags: "g", shouldPass: false }, // Invalid - nothing to repeat
            { pattern: "valid.*pattern", flags: "gim", shouldPass: true }
        ];
        
        let passed = 0;
        let total = testCases.length;
        
        for (let i = 0; i < testCases.length; i++) {
            let test = testCases[i];
            let result = regexFindReplace.validateRegex(test.pattern, test.flags);
            
            if (result.valid === test.shouldPass) {
                console.println("✓ Test " + (i + 1) + " passed: " + test.pattern);
                passed++;
            } else {
                console.println("✗ Test " + (i + 1) + " failed: " + test.pattern);
                console.println("  Expected: " + test.shouldPass + ", Got: " + result.valid);
                if (result.error) {
                    console.println("  Error: " + result.error);
                }
            }
        }
        
        console.println("Regex Validation Tests: " + passed + "/" + total + " passed");
        return passed === total;
    },
    
    // Test regex escaping
    testRegexEscaping: function() {
        console.println("=== Testing Regex Escaping ===");
        
        let testCases = [
            { input: "test.string", expected: "test\\.string" },
            { input: "price: $100", expected: "price: \\$100" },
            { input: "question?", expected: "question\\?" },
            { input: "array[0]", expected: "array\\[0\\]" },
            { input: "normal text", expected: "normal text" },
            { input: "regex.*+?^${}()|[]\\", expected: "regex\\.\\*\\+\\?\\^\\$\\{\\}\\(\\)\\|\\[\\]\\\\" }
        ];
        
        let passed = 0;
        let total = testCases.length;
        
        for (let i = 0; i < testCases.length; i++) {
            let test = testCases[i];
            let result = regexFindReplace.escapeRegex(test.input);
            
            if (result === test.expected) {
                console.println("✓ Test " + (i + 1) + " passed: " + test.input);
                passed++;
            } else {
                console.println("✗ Test " + (i + 1) + " failed: " + test.input);
                console.println("  Expected: " + test.expected);
                console.println("  Got: " + result);
            }
        }
        
        console.println("Regex Escaping Tests: " + passed + "/" + total + " passed");
        return passed === total;
    },
    
    // Test flag building
    testFlagBuilding: function() {
        console.println("=== Testing Flag Building ===");
        
        let testCases = [
            { config: { caseSensitive: false, multiline: false, dotAll: false }, expected: "gi" },
            { config: { caseSensitive: true, multiline: false, dotAll: false }, expected: "g" },
            { config: { caseSensitive: false, multiline: true, dotAll: false }, expected: "gim" },
            { config: { caseSensitive: false, multiline: false, dotAll: true }, expected: "gis" },
            { config: { caseSensitive: true, multiline: true, dotAll: true }, expected: "gms" }
        ];
        
        let passed = 0;
        let total = testCases.length;
        
        for (let i = 0; i < testCases.length; i++) {
            let test = testCases[i];
            let result = regexFindReplace.buildRegexFlags(test.config);
            
            if (result === test.expected) {
                console.println("✓ Test " + (i + 1) + " passed");
                passed++;
            } else {
                console.println("✗ Test " + (i + 1) + " failed");
                console.println("  Expected: " + test.expected);
                console.println("  Got: " + result);
            }
        }
        
        console.println("Flag Building Tests: " + passed + "/" + total + " passed");
        return passed === total;
    },
    
    // Test batch operation parsing
    testBatchParsing: function() {
        console.println("=== Testing Batch Operation Parsing ===");
        
        let validBatch = JSON.stringify([
            { find: "test1", replace: "replacement1", useRegex: true },
            { find: "test2", replace: "replacement2", useRegex: false }
        ]);
        
        let invalidBatch = "{ invalid json }";
        
        let emptyBatch = "[]";
        
        let testCases = [
            { input: validBatch, shouldPass: true, description: "Valid batch operations" },
            { input: invalidBatch, shouldPass: false, description: "Invalid JSON" },
            { input: emptyBatch, shouldPass: true, description: "Empty batch array" }
        ];
        
        let passed = 0;
        let total = testCases.length;
        
        for (let i = 0; i < testCases.length; i++) {
            let test = testCases[i];
            let success = false;
            
            try {
                let parsed = JSON.parse(test.input);
                success = Array.isArray(parsed);
            } catch (e) {
                success = false;
            }
            
            if (success === test.shouldPass) {
                console.println("✓ Test " + (i + 1) + " passed: " + test.description);
                passed++;
            } else {
                console.println("✗ Test " + (i + 1) + " failed: " + test.description);
            }
        }
        
        console.println("Batch Parsing Tests: " + passed + "/" + total + " passed");
        return passed === total;
    },
    
    // Test pattern library
    testPatternLibrary: function() {
        console.println("=== Testing Pattern Library ===");
        
        let testStrings = {
            email: [
                { text: "Contact <EMAIL> for info", shouldMatch: true },
                { text: "Email: <EMAIL>", shouldMatch: true },
                { text: "Invalid email: @domain.com", shouldMatch: false },
                { text: "Not an email: john.example.com", shouldMatch: false }
            ],
            phone: [
                { text: "Call ************ today", shouldMatch: true },
                { text: "Phone: (*************", shouldMatch: false }, // Pattern doesn't handle parentheses
                { text: "Number: ************", shouldMatch: true },
                { text: "Invalid: 123-45-6789", shouldMatch: false }
            ],
            url: [
                { text: "Visit https://www.example.com", shouldMatch: true },
                { text: "Go to http://domain.org/path", shouldMatch: true },
                { text: "Invalid: ftp://example.com", shouldMatch: false },
                { text: "Not URL: www.example.com", shouldMatch: false }
            ]
        };
        
        let totalPassed = 0;
        let totalTests = 0;
        
        for (let patternName in testStrings) {
            if (regexFindReplace.patterns[patternName]) {
                console.println("Testing " + patternName + " pattern:");
                let pattern = regexFindReplace.patterns[patternName];
                let tests = testStrings[patternName];
                
                for (let i = 0; i < tests.length; i++) {
                    let test = tests[i];
                    let matches = test.text.match(pattern);
                    let hasMatch = matches !== null && matches.length > 0;
                    
                    totalTests++;
                    if (hasMatch === test.shouldMatch) {
                        console.println("  ✓ " + test.text);
                        totalPassed++;
                    } else {
                        console.println("  ✗ " + test.text);
                        console.println("    Expected match: " + test.shouldMatch + ", Got: " + hasMatch);
                    }
                }
            }
        }
        
        console.println("Pattern Library Tests: " + totalPassed + "/" + totalTests + " passed");
        return totalPassed === totalTests;
    },
    
    // Run all tests
    runAllTests: function() {
        console.println("========================================");
        console.println("Enhanced Regex Find/Replace Plugin Tests");
        console.println("========================================");
        
        let results = [];
        results.push(this.testRegexValidation());
        results.push(this.testRegexEscaping());
        results.push(this.testFlagBuilding());
        results.push(this.testBatchParsing());
        results.push(this.testPatternLibrary());
        
        let passed = results.filter(r => r).length;
        let total = results.length;
        
        console.println("========================================");
        console.println("Overall Test Results: " + passed + "/" + total + " test suites passed");
        
        if (passed === total) {
            console.println("🎉 All tests passed! Plugin is working correctly.");
        } else {
            console.println("⚠️  Some tests failed. Check the output above for details.");
        }
        
        return passed === total;
    },
    
    // Interactive test function
    interactiveTest: function() {
        console.println("=== Interactive Plugin Test ===");
        console.println("This will open the plugin dialog for manual testing.");
        console.println("Try these test scenarios:");
        console.println("1. Simple text replacement (disable regex)");
        console.println("2. Email pattern matching: \\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b");
        console.println("3. Phone number formatting: \\b(\\d{3})[-.]?(\\d{3})[-.]?(\\d{4})\\b -> ($1) $2-$3");
        console.println("4. Batch mode with multiple operations");
        
        // This would open the actual plugin dialog
        if (typeof regexFindReplace !== 'undefined' && regexFindReplace.run) {
            try {
                regexFindReplace.run(this);
            } catch (e) {
                console.println("Error running interactive test: " + e.message);
                console.println("Make sure a PDF document is open and the plugin is properly loaded.");
            }
        } else {
            console.println("Plugin not found. Make sure regex-find-replace-plugin.js is loaded.");
        }
    }
};

// Auto-run tests when this file is loaded
console.println("Test suite loaded. Run regexTestSuite.runAllTests() to execute all tests.");
console.println("Or run regexTestSuite.interactiveTest() for manual testing.");

// Uncomment the next line to auto-run tests on load
// regexTestSuite.runAllTests();
